"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="w-full bg-black text-white shadow-md">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Image
            src="/logo.png"
            alt="Free Games Online Logo"
            width={80}
            height={80}
            className="w-12 h-12 rounded-md"
          />
          <span className="text-xl font-bold text-white">Free Games Online</span>
        </Link>
        
        <div className="hidden md:flex space-x-6">
          <Link href="/" className="text-white hover:text-purple-400 transition-colors">
            Home
          </Link>
          <Link href="/sprunki-treatment-brud" className="text-white hover:text-purple-400 transition-colors">
           Sprunki Treatment Brud
          </Link>
          <Link href="/sprunki-wenda-treatment" className="text-white hover:text-purple-400 transition-colors">
           Sprunki Wenda Treatment
          </Link>


          <Link href="/sprunki-sky-treatment" className="text-white hover:text-purple-400 transition-colors">
           Sprunki Sky Treatment
          </Link>
          
          <Link href="/contact" className="text-white hover:text-purple-400 transition-colors">
            Contact
          </Link>
          <Link href="/privacy-policy" className="text-white hover:text-purple-400 transition-colors">
            Privacy Policy
          </Link>
        </div>

        <button 
          className="md:hidden text-white focus:outline-none" 
          onClick={toggleMenu}
          aria-expanded={isMenuOpen}
          aria-label="Toggle navigation menu"
        >
          {isMenuOpen ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          )}
        </button>
      </div>

      {/* Mobile menu dropdown */}
      {isMenuOpen && (
        <div className="md:hidden bg-black border-t border-gray-800">
          <div className="container mx-auto px-4 py-2 flex flex-col space-y-3">
            <Link href="/" className="text-white hover:text-purple-400 transition-colors py-2">
              Home
            </Link>
            <Link href="/sprunki-treatment-brud" className="text-white hover:text-purple-400 transition-colors py-2">
             Sprunki Treatment Brud
            </Link>
            <Link href="/sprunki-wenda-treatment" className="text-white hover:text-purple-400 transition-colors py-2">
              Sprunki Wenda Treatment
            </Link>
            <Link href="/sprunki-sky-treatment" className="text-white hover:text-purple-400 transition-colors py-2">
              Sprunki Sky Treatment
            </Link>
            <Link href="/contact" className="text-white hover:text-purple-400 transition-colors py-2">
              Contact
            </Link>
            <Link href="/privacy-policy" className="text-white hover:text-purple-400 transition-colors py-2">
              Privacy Policy
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
}