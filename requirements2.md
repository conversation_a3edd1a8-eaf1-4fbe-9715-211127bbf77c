这是一个SEO友好型在线免费游戏网站项目，需要把这个项目部署到cloudflare pages，写代码要能在cloudflare pages部署，要符合cloudflare pages规范：

技术要求：

1.Next.js App Router（使用 /app 目录）
2.游戏框是直接使用iframe嵌入游戏，我会把可用的游戏iframe链接放在supabase
2.游戏详情页内容采用 Markdown 存储在数据库supabase
3.Static Site Generation (SSG) 为主要渲染方式
4.使用 Tailwind CSS、shadcn/ui 或 vanilla-extract 保持语义化结构
5.页面渲染要有完整的<!DOCTYPE html>、<html lang="en">、<head>和<body>结构

页面渲染策略：

页面	推荐渲染方式	原因
首页	SSG	快速加载，内容较固定
分类页	SSG / ISR	可提前生成静态页面，也可用 revalidate 动态更新
游戏详情页	SSG	最适合 SEO，每个页面独立、内容丰富，关键页面，必须源码可抓取

💡 所有页面必须 SSR / SSG 输出 HTML，避免 use client 让页面变成 CSR。


语义化标签	使用 <main> <article> <section> <h1> <h2> <p> 等
静态渲染	首页、分类页、详情页都使用 SSG 或 SSR，禁止 CSR-only 内容
OpenGraph	支持 og:title og:image og:description 用于社交分享
Schema.org	游戏详情页使用结构化数据，如 @type: VideoGame 或 Game
Robots.txt + Sitemap	自动生成，建议使用 next-sitemap


网站结构：
首页
分类页
游戏详情页
